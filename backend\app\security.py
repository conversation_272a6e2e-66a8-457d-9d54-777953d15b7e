from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
import os
from .config import settings

def _get_fernet():
    key = os.getenv("FERNET_SECRET") or settings.fernet_secret
    if not key:
        raise RuntimeError("FERNET_SECRET is not set")
    if isinstance(key, str):
        key = key.encode()
    return Fernet(key)

def encrypt_value(plaintext: str) -> str:
    f = _get_fernet()
    return f.encrypt(plaintext.encode()).decode()

def decrypt_value(token: str) -> str:
    f = _get_fernet()
    try:
        return f.decrypt(token.encode()).decode()
    except InvalidToken:
        return ""
