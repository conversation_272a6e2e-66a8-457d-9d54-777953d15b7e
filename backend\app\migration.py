import os, time, json
from .config import settings
from .oracle_helper import connect_plant
from . import crud, models
from sqlalchemy.orm import Session

BATCH_SIZE = int(os.getenv('BATCH_SIZE', settings.batch_size))
RETRY_LIMIT = int(os.getenv('RETRY_LIMIT', settings.retry_limit))

def run_migration_for_plant_prod(db: Session, run_id: int, plant, mapping_json: dict, limit=None):
    api_url = settings.paperless_api_url or os.getenv('PAPERLESS_API_URL')
    token = settings.paperless_api_token or os.getenv('PAPERLESS_ADMIN_TOKEN')
    conn = connect_plant(plant)
    cur = conn.cursor()
    try:
        cur.execute("SELECT COUNT(*) FROM CSDOCS")
        total = cur.fetchone()[0]
    except Exception:
        total = 0
    crud.update_run_status(db, run_id, total=total, status='running')
    offset = 0
    migrated = 0
    failed = 0
    while True:
        try:
            cur.execute(f"SELECT DKEY, DOCPATH, DOCNAME, TABLENAME, RECORDKEY, FILESIZE FROM (SELECT a.*, ROWNUM rnum FROM (SELECT * FROM CSDOCS ORDER BY DKEY) a WHERE ROWNUM <= :max) WHERE rnum > :offset", max=offset+BATCH_SIZE, offset=offset)
            rows = cur.fetchall()
        except Exception:
            rows = []
        if not rows:
            break
        for row in rows:
            dkey, docpath, docname, tablename, recordkey, filesize = row[0], row[1], row[2], row[3], row[4], row[5]
            meta = {}
            try:
                cur2 = conn.cursor()
                cur2.execute(f"SELECT * FROM {tablename} WHERE RKEY = :rk", rk=recordkey)
                meta_row = cur2.fetchone()
                if meta_row:
                    cols = [c[0] for c in cur2.description]
                    meta = dict(zip(cols, meta_row))
            except Exception:
                meta = {}
            combined = {'DOCNAME': docname, 'DOCPATH': docpath}
            combined.update(meta)
            payload = {}
            tags = []
            for col, target in mapping_json.items():
                val = combined.get(col)
                if val is None: continue
                if str(target).startswith('tag:'):
                    tags.append(str(val))
                elif str(target).startswith('custom:'):
                    payload.setdefault('custom_fields', {})[target.split(':',1)[1]] = val
                else:
                    payload[target] = val
            payload['tags'] = tags
            if not os.path.exists(docpath):
                crud.add_run_log(db, run_id, dkey, docname, 'failed', f'file not found: {docpath}')
                failed += 1; crud.update_run_status(db, run_id, failed=failed)
                continue
            attempt = 0
            last_resp = None
            while attempt < RETRY_LIMIT:
                status_code, resp = crud._upload_document_helper(api_url, token, docpath, payload)
                last_resp = resp
                attempt += 1
                if status_code and status_code in (200,201):
                    paperless_id = resp.get('id') if isinstance(resp, dict) else None
                    crud.add_run_log(db, run_id, dkey, docname, 'success', str(status_code), attempts=attempt, paperless_id=paperless_id)
                    migrated += 1; crud.update_run_status(db, run_id, succeeded=migrated)
                    break
                time.sleep(1)
            else:
                crud.add_run_log(db, run_id, dkey, docname, 'failed', f'upload failed after {RETRY_LIMIT} attempts: {last_resp}', attempts=attempt)
                failed += 1; crud.update_run_status(db, run_id, failed=failed)
            if limit and (migrated+failed) >= limit:
                break
        offset += BATCH_SIZE
        if settings.demo_mode or (limit and (migrated+failed) >= limit):
            break
    conn.close()
    crud.update_run_status(db, run_id, status='finished')
    return {'total': total, 'migrated': migrated, 'failed': failed}

def run_single_log_retry(db: Session, log_id: int):
    # simplified retry using the same mapping and plant
    log = crud.get_log(db, log_id)
    if not log:
        return {'error': 'log not found'}
    run = db.query(models.MigrationRun).filter(models.MigrationRun.id==log.run_id).first()
    mapping = db.query(models.Mapping).filter(models.Mapping.id==run.mapping_id).first()
    plant = db.query(models.Plant).filter(models.Plant.id==run.plant_id).first()
    mapping_json = json.loads(mapping.field_mappings)
    conn = connect_plant(plant)
    cur = conn.cursor()
    try:
        cur.execute("SELECT DKEY, DOCPATH, DOCNAME, TABLENAME, RECORDKEY, FILESIZE FROM CSDOCS WHERE DKEY = :dkey", dkey=log.doc_dkey)
        row = cur.fetchone()
        if not row:
            return {'error': 'doc not found in CSDOCS'}
        dkey, docpath, docname, tablename, recordkey, filesize = row[0], row[1], row[2], row[3], row[4], row[5]
        cur2 = conn.cursor()
        cur2.execute(f"SELECT * FROM {tablename} WHERE RKEY = :rk", rk=recordkey)
        meta_row = cur2.fetchone()
        meta = {}
        if meta_row:
            cols = [c[0] for c in cur2.description]
            meta = dict(zip(cols, meta_row))
        combined = {'DOCNAME': docname, 'DOCPATH': docpath}
        combined.update(meta)
        payload = {}
        tags = []
        for col, target in mapping_json.items():
            val = combined.get(col)
            if val is None: continue
            if str(target).startswith('tag:'):
                tags.append(str(val))
            elif str(target).startswith('custom:'):
                payload.setdefault('custom_fields', {})[target.split(':',1)[1]] = val
            else:
                payload[target] = val
        payload['tags'] = tags
        api_url = settings.paperless_api_url or os.getenv('PAPERLESS_API_URL')
        token = settings.paperless_api_token or os.getenv('PAPERLESS_ADMIN_TOKEN')
        status_code, resp = crud._upload_document_helper(api_url, token, docpath, payload)
        if status_code and status_code in (200,201):
            paperless_id = resp.get('id') if isinstance(resp, dict) else None
            crud.update_log_attempts(db, log.id, attempts=log.attempts+1, status='success', message=str(status_code), paperless_id=paperless_id)
            return {'status':'success'}
        else:
            crud.update_log_attempts(db, log.id, attempts=log.attempts+1, status='failed', message=str(resp))
            return {'status':'failed','detail':str(resp)}
    finally:
        conn.close()
