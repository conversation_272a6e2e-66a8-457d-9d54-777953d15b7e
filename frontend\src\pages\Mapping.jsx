import React, {useEffect, useState} from 'react'
import { api } from '../lib/api'
import { useStore } from '../store/useStore'

// simple similarity function
function score(a,b){ if(!a||!b) return 0; a=a.toLowerCase(); b=b.toLowerCase(); if(a===b) return 1; return (a.split('').filter(c=>b.includes(c)).length)/Math.max(a.length,b.length) }

export default function Mapping(){
  const selectedPlant = useStore(s=>s.selectedPlant)
  const [tables,setTables]=useState([])
  const [selectedTable,setSelectedTable]=useState('')
  const [columns,setColumns]=useState([])
  const [paperlessFields,setPaperlessFields]=useState([])
  const [mapping,setMapping]=useState({})

  useEffect(()=>{ if(selectedPlant) loadTables(); fetchPaperless() },[selectedPlant])
  async function loadTables(){ try{ const r=await api.get(`/metadata/tables/${selectedPlant.id}`); setTables(r.data) }catch(e){console.error(e)} }
  async function fetchColumns(t){ setSelectedTable(t); try{ const r=await api.get(`/metadata/columns/${selectedPlant.id}/${t}`); setColumns(r.data); // autosuggest
      const sugg = {};
      for(const c of r.data){
        let best=null, bestScore=0;
        for(const f of paperlessFields){ const s = score(c.column_name,f); if(s>bestScore){bestScore=s; best=f} }
        if(bestScore>0.4) sugg[c.column_name]=best
      }
      setMapping(sugg);
    }catch(e){console.error(e)} }
  async function fetchPaperless(){ try{ const r=await api.get('/paperless/fields'); setPaperlessFields(r.data) }catch(e){ console.warn('paperless fields not available') } }
  function setMap(col,v){ setMapping({...mapping, [col]:v}) }
  async function save(){ await api.post('/mappings', {plant_id:selectedPlant.id, metadata_table:selectedTable, field_mappings:mapping}); alert('saved') }
  return (<div className='bg-white p-6 rounded shadow'>
    <h3 className='text-lg font-semibold mb-4'>Field Mapping</h3>
    {!selectedPlant? <div className='text-slate-500'>Select a plant first</div> : (
      <div>
        <div className='mb-3'><h4 className='font-medium'>Tables</h4><div className='mt-2 flex gap-2 flex-wrap'>{tables.map(t=> (<button key={t} className='px-3 py-1 bg-slate-100 rounded' onClick={()=>fetchColumns(t)}>{t}</button>))}</div></div>
        <h4 className='font-medium'>Columns for: <span className='font-normal'>{selectedTable}</span></h4>
        <div className='mt-2 overflow-x-auto'>
          <table className='min-w-full'><thead><tr className='bg-slate-50'><th className='p-2'>Column</th><th className='p-2'>Type</th><th className='p-2'>Map to (Paperless)</th></tr></thead>
            <tbody>{columns.map(c=>(<tr key={c.column_name}><td className='p-2'>{c.column_name}</td><td className='p-2'>{c.data_type}</td><td className='p-2'><select value={mapping[c.column_name]||''} onChange={e=>setMap(c.column_name,e.target.value)} className='p-2 border rounded w-full'><option value=''>-- none --</option>{paperlessFields.map(f=>(<option key={f} value={f}>{f}</option>))}</select></td></tr>))}</tbody></table>
        </div>
        <div className='mt-4'><button className='px-4 py-2 bg-sky-600 text-white rounded' onClick={save}>Save Mapping</button></div>
      </div>
    )}
  </div>)
}
