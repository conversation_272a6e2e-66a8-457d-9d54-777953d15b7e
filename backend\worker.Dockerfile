FROM public.ecr.aws/docker/library/python:3.11-slim-bookworm
WORKDIR /app
COPY ./tools/instantclient-basic-linuxx64.zip /tmp/instantclient-basic-linuxx64.zip
RUN apt-get update && apt-get install -y build-essential libaio1 wget unzip python3-dev alien && \
    # Change version based on https://www.oracle.com/de/database/technologies/instant-client/linux-x86-64-downloads.html
    # Example alternative: http://yum.oracle.com/repo/OracleLinux/OL7/oracle/instantclient/x86_64/getPackage/oracle-instantclient19.5-basiclite-********.0-1.x86_64.rpm 
    wget http://yum.oracle.com/repo/OracleLinux/OL7/oracle/instantclient/x86_64/getPackage/oracle-instantclient19.6-basic-********.0-1.x86_64.rpm && \
    alien -i --scripts oracle-instantclient*.rpm && \
    rm -f oracle-instantclient*.rpm     && \
    echo /opt/oracle/instantclient_19_6 > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig
ENV LD_LIBRARY_PATH=/opt/oracle/instantclient_19_6:$LD_LIBRARY_PATH
COPY ./app ./app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
CMD ["celery","-A","app.celery_worker.celery","worker","--loglevel=info"]
