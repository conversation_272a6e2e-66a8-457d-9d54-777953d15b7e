# Note: cx_Oracle required; in demo mode this module may not be used.
import cx_Oracle
from .security import decrypt_value

def connect_plant(plant):
    pwd = decrypt_value(plant.password_enc) if plant.password_enc else None
    if not pwd:
        raise RuntimeError('No password available for plant')
    port = int(plant.port) if plant.port else 1521
    dsn = cx_Oracle.makedsn(plant.host, port, service_name=plant.service_name)
    conn = cx_Oracle.connect(plant.username, pwd, dsn, encoding='UTF-8', nencoding='UTF-8')
    return conn
