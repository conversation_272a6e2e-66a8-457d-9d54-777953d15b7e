import React, {useEffect, useState} from 'react'
import { api } from '../lib/api'
import { useStore } from '../store/useStore'
export default function Migrations(){
  const selectedPlant = useStore(s=>s.selectedPlant)
  const [mappings,setMappings]=useState([])
  const [runs,setRuns]=useState([])
  const [selectedMapping,setSelectedMapping]=useState(null)
  useEffect(()=>{ loadRuns(); if(selectedPlant) loadMappings() },[selectedPlant])
  async function loadMappings(){ const r=await api.get(`/mappings/${selectedPlant.id}`); setMappings(r.data) }
  async function loadRuns(){ const r=await api.get('/migrate/runs'); setRuns(r.data) }
  async function start(){ if(!selectedMapping) return alert('select mapping'); const r=await api.post('/migrate/start', null, {params:{plant_id:selectedPlant.id, mapping_id:selectedMapping.id}}); alert('started: '+r.data.task_id); loadRuns() }
  async function viewLogs(run){ const r=await api.get(`/migrate/runs/${run.id}/logs`); alert('logs count: '+r.data.length) }
  return (<div className='bg-white p-6 rounded shadow'>
    <h3 className='text-lg font-semibold mb-4'>Migrations</h3>
    {!selectedPlant? <div className='text-slate-500'>Select a plant first</div> : (
      <div>
        <div className='mb-3'><h4 className='font-medium'>Mappings</h4><div className='mt-2 space-y-2'>{mappings.map(m=> (<div key={m.id} className='p-2 border rounded flex justify-between items-center'><div>{m.metadata_table}</div><div><button onClick={()=>setSelectedMapping(m)} className='px-2 py-1 bg-sky-600 text-white rounded'>Select</button></div></div>))}</div></div>
        <div className='mb-3'><button className='px-4 py-2 bg-emerald-600 text-white rounded' onClick={start}>Start Migration</button></div>
        <div><h4 className='font-medium'>Runs</h4><div className='mt-2 space-y-2'>{runs.map(r=> (<div key={r.id} className='p-2 border rounded flex justify-between items-center'><div>Run #{r.id} - {r.status} - total:{r.total} succeeded:{r.succeeded} failed:{r.failed}</div><div><button onClick={()=>viewLogs(r)} className='px-2 py-1 bg-sky-600 text-white rounded'>Logs</button></div></div>))}</div></div>
      </div>
    )}
  </div>)
}
