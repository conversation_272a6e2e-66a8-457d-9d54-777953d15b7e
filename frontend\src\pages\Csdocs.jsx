import React, {useEffect, useState} from 'react'
import { api } from '../lib/api'
import { useStore } from '../store/useStore'
export default function Csdocs(){
  const selectedPlant = useStore(s=>s.selectedPlant)
  const [docs,setDocs]=useState([])
  const [q,setQ]=useState('')
  useEffect(()=>{ if(selectedPlant) load() },[selectedPlant])
  async function load(){ try{ const r = await api.get('/csdocs', {params:{plant_id:selectedPlant?.id}}); setDocs(r.data) }catch(e){console.error(e)} }
  async function search(){ try{ const r = await api.get('/csdocs', {params:{plant_id:selectedPlant?.id, q}}); setDocs(r.data) }catch(e){console.error(e)} }
  return (<div className='bg-white p-6 rounded shadow'>
    <h3 className='text-lg font-semibold mb-4'>CSDOCS Browser</h3>
    {!selectedPlant? (<div className='text-slate-500'>Select a plant in the Plants page</div>): (
      <div>
        <div className='flex gap-2 mb-3'><input value={q} onChange={e=>setQ(e.target.value)} placeholder='search docname or path' className='p-2 border rounded flex-1' /><button onClick={search} className='px-3 py-2 bg-sky-600 text-white rounded'>Search</button></div>
        <div className='overflow-auto max-h-96'>
          <table className='min-w-full'><thead><tr className='bg-slate-50'><th className='p-2'>DKEY</th><th className='p-2'>DOCNAME</th><th className='p-2'>DOCPATH</th></tr></thead>
            <tbody>{docs.map(d=>(<tr key={d.DKEY} className='border-t'><td className='p-2'>{d.DKEY}</td><td className='p-2'>{d.DOCNAME}</td><td className='p-2'>{d.DOCPATH}</td></tr>))}</tbody></table>
        </div>
      </div>
    )}
  </div>)
}
