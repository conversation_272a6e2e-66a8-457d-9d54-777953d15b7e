version: '3.8'
services:
  redis:
    image: redis:7-alpine
  backend:
    build: ./backend
    env_file: ./backend/.env
    volumes:
      - ./backend/data:/app/data
    ports:
      - '8000:8000'
    depends_on:
      - redis
  worker:
    build:
      context: ./backend
      dockerfile: worker.Dockerfile
    env_file: ./backend/.env
    depends_on:
      - redis
  frontend:
    build: ./frontend
    ports:
      - '5173:80'
    depends_on:
      - backend
