{"name": "pp-to-paperless-frontend-v2", "version": "0.0.1", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.4.0", "react-router-dom": "^6.11.2", "zustand": "^4.4.0"}, "devDependencies": {"vite": "^5.0.0", "@vitejs/plugin-react": "^4.0.0", "tailwindcss": "^4.1.14", "@tailwindcss/postcss": "^4.1.13", "postcss": "^8.4.24", "autoprefixer": "^10.4.14"}}