from sqlalchemy.orm import Session
from . import models, schemas, security
import json, os

def create_plant(db: Session, plant: schemas.PlantCreate):
    p = models.Plant(name=plant.name, host=plant.host or "", port=plant.port or "", username=plant.username or "", password_enc=security.encrypt_value(plant.password) if plant.password else None)
    db.add(p); db.commit(); db.refresh(p); return p

def get_plants(db: Session): return db.query(models.Plant).all()

def get_plant(db: Session, plant_id: int): return db.query(models.Plant).filter(models.Plant.id==plant_id).first()

def save_mapping(db: Session, mapping: schemas.MappingIn):
    m = db.query(models.Mapping).filter(models.Mapping.plant_id==mapping.plant_id, models.Mapping.metadata_table==mapping.metadata_table).first()
    if m is None:
        m = models.Mapping(plant_id=mapping.plant_id, metadata_table=mapping.metadata_table, field_mappings=json.dumps(mapping.field_mappings))
        db.add(m)
    else:
        m.field_mappings = json.dumps(mapping.field_mappings)
    db.commit(); db.refresh(m); return m

def get_mappings_for_plant(db: Session, plant_id: int): return db.query(models.Mapping).filter(models.Mapping.plant_id==plant_id).all()

def create_migration_run(db: Session, plant_id: int, mapping_id: int):
    run = models.MigrationRun(plant_id=plant_id, mapping_id=mapping_id, status='pending')
    db.add(run); db.commit(); db.refresh(run); return run

def update_run_status(db: Session, run_id: int, **kwargs):
    r = db.query(models.MigrationRun).filter(models.MigrationRun.id==run_id).first()
    for k,v in kwargs.items(): setattr(r, k, v)
    db.commit(); db.refresh(r); return r

def add_run_log(db: Session, run_id: int, doc_dkey: int, doc_name: str, status: str, message: str | None = None, attempts: int = 0, paperless_id: int | None = None):
    log = models.MigrationLog(run_id=run_id, doc_dkey=doc_dkey, doc_name=doc_name, status=status, message=message, attempts=attempts, paperless_id=paperless_id)
    db.add(log); db.commit(); db.refresh(log); return log

def update_log_attempts(db: Session, log_id: int, attempts: int, status: str = None, message: str = None, paperless_id: int | None = None):
    l = db.query(models.MigrationLog).filter(models.MigrationLog.id==log_id).first()
    if l is None: return None
    l.attempts = attempts
    if status: l.status = status
    if message: l.message = message
    if paperless_id is not None: l.paperless_id = paperless_id
    db.commit(); db.refresh(l); return l

def get_run_logs(db: Session, run_id: int): return db.query(models.MigrationLog).filter(models.MigrationLog.run_id==run_id).all()

def get_log(db: Session, log_id: int): return db.query(models.MigrationLog).filter(models.MigrationLog.id==log_id).first()

# upload helper (simple wrapper)
from .crud_upload_helper import _upload_document_helper
