import React from 'react'
import { Routes, Route, Link } from 'react-router-dom'
import Plants from './pages/Plants'
import Csdocs from './pages/Csdocs'
import Mapping from './pages/Mapping'
import Migrations from './pages/Migrations'
import Paperless from './pages/Paperless'

export default function App(){
  return (
    <div className='min-h-screen bg-slate-50'>
      <div className='max-w-7xl mx-auto p-4 grid grid-cols-4 gap-4'>
        <aside className='col-span-1 bg-white p-4 rounded shadow'>
          <h2 className='text-xl font-semibold mb-4'>PP → Paperless</h2>
          <nav className='flex flex-col gap-2'>
            <Link to='/' className='text-slate-700 hover:text-sky-600'>Csdocs</Link>
            <Link to='/mapping' className='text-slate-700 hover:text-sky-600'>Mapping</Link>
            <Link to='/migrations' className='text-slate-700 hover:text-sky-600'>Migrations</Link>
            <Link to='/plants' className='text-slate-700 hover:text-sky-600'>Plants</Link>
            <Link to='/paperless' className='text-slate-700 hover:text-sky-600'>Paperless</Link>
          </nav>
        </aside>
        <main className='col-span-3'>
          <Routes>
            <Route path='/' element={<Csdocs/>} />
            <Route path='/mapping' element={<Mapping/>} />
            <Route path='/migrations' element={<Migrations/>} />
            <Route path='/plants' element={<Plants/>} />
            <Route path='/paperless' element={<Paperless/>} />
          </Routes>
        </main>
      </div>
    </div>
  )
}
