from .celery_worker import celery
from .database import SessionLocal
from . import crud
from .migration import run_migration_for_plant_prod, run_single_log_retry

@celery.task(bind=True)
def migrate_plant_task(self, plant_id, mapping_id, limit=None):
    db = SessionLocal()
    plant = crud.get_plant(db, plant_id)
    if not plant:
        return {'error': 'plant not found'}
    mappings = crud.get_mappings_for_plant(db, plant_id)
    mapping = None
    for m in mappings:
        if m.id == mapping_id:
            mapping = m
            break
    if not mapping:
        return {'error': 'mapping not found'}
    import json
    mapping_json = json.loads(mapping.field_mappings)
    run = crud.create_migration_run(db, plant_id, mapping_id)
    result = run_migration_for_plant_prod(db, run.id, plant, mapping_json, limit=limit)
    return result

@celery.task(bind=True)
def retry_log_task(self, log_id):
    db = SessionLocal()
    result = run_single_log_retry(db, log_id)
    return result
