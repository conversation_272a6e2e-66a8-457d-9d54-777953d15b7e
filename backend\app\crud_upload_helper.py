import os, json, requests

def _upload_document_helper(api_url, token, filepath, payload):
    headers = {}
    if token: headers['Authorization'] = f"Token {token}"
    files = {}
    if filepath and os.path.exists(filepath):
        files['document'] = open(filepath, 'rb')
    data = {k: (json.dumps(v) if isinstance(v,(dict,list)) else v) for k,v in payload.items()}
    try:
        r = requests.post(f"{api_url.rstrip('/')}/documents/post_document/", data=data, files=files, headers=headers, timeout=60)
        if files.get('document'): files['document'].close()
        try:
            return r.status_code, r.json()
        except Exception:
            return r.status_code, {}
    except Exception as e:
        if files.get('document'): files['document'].close()
        return None, {'error': str(e)}
