import React, {useEffect, useState} from 'react'
import { api } from '../lib/api'
import { useStore } from '../store/useStore'
export default function Plants(){
  const [plants,setPlants]=useState([])
  const [form,setForm]=useState({name:'',host:'',port:'1521',service_name:'',username:'',password:''})
  const setSelectedPlant = useStore(s=>s.setSelectedPlant)
  useEffect(()=>load(),[])
  async function load(){ try{ const r = await api.get('/plants'); setPlants(r.data) }catch(e){console.error(e)} }
  function handle(e){ setForm({...form,[e.target.name]: e.target.value}) }
  async function submit(e){ e.preventDefault(); await api.post('/plants', form); setForm({name:'',host:'',port:'1521',service_name:'',username:'',password:''}); load() }
  return (<div className='bg-white p-6 rounded shadow'>
    <h3 className='text-lg font-semibold mb-4'>Plants</h3>
    <form onSubmit={submit} className='grid grid-cols-2 gap-3 mb-4'>
      <input name='name' value={form.name} onChange={handle} placeholder='Name' className='p-2 border rounded' required />
      <input name='host' value={form.host} onChange={handle} placeholder='Host' className='p-2 border rounded' />
      <input name='port' value={form.port} onChange={handle} placeholder='Port' className='p-2 border rounded' />
      <input name='service_name' value={form.service_name} onChange={handle} placeholder='Service name' className='p-2 border rounded' />
      <input name='username' value={form.username} onChange={handle} placeholder='Username' className='p-2 border rounded' />
      <input type='password' name='password' value={form.password} onChange={handle} placeholder='Password' className='p-2 border rounded' />
      <div></div>
      <button className='px-4 py-2 bg-sky-600 text-white rounded' type='submit'>Add Plant</button>
    </form>
    <div>
      <table className='min-w-full'>
        <thead><tr className='bg-slate-50'><th className='p-2 text-left'>Name</th><th className='p-2 text-left'>Host</th><th className='p-2 text-left'>Port</th><th className='p-2 text-left'>Service</th><th className='p-2'></th></tr></thead>
        <tbody>{plants.map(p=>(<tr key={p.id} className='border-t'><td className='p-2'>{p.name}</td><td className='p-2'>{p.host}</td><td className='p-2'>{p.port}</td><td className='p-2'>{p.service_name||''}</td><td className='p-2'><button onClick={()=>setSelectedPlant(p)} className='px-2 py-1 bg-slate-700 text-white rounded'>Select</button></td></tr>))}</tbody>
      </table>
    </div>
  </div>)
}
