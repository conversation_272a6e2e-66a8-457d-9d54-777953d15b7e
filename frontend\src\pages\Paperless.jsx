import React, {useState} from 'react'
import { api } from '../lib/api'
export default function Paperless(){
  const [form,setForm]=useState({api_url:'', token:''})
  function handle(e){ setForm({...form,[e.target.name]: e.target.value}) }
  async function test(){ try{ const r = await api.post('/paperless/test', form); alert(JSON.stringify(r.data)) }catch(e){ alert('error') } }
  return (<div className='bg-white p-6 rounded shadow'><h3 className='text-lg font-semibold mb-4'>Paperless</h3><div className='grid grid-cols-2 gap-3'><input name='api_url' value={form.api_url} onChange={handle} placeholder='API URL' className='p-2 border rounded' /><input name='token' value={form.token} onChange={handle} placeholder='Admin Token' className='p-2 border rounded' /><button className='px-4 py-2 bg-sky-600 text-white rounded' onClick={test}>Test</button></div></div>) }
