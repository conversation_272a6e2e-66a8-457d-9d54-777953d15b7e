from fastapi import FastAPI, Depends, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from .database import engine, Base, get_db
from . import models, crud, schemas
from sqlalchemy.orm import Session
from .celery_tasks import migrate_plant_task, retry_log_task
Base.metadata.create_all(bind=engine)
app = FastAPI(title='pp_to_paperless v8.3 (merged)')
app.add_middleware(CORSMiddleware, allow_origins=['*'], allow_credentials=True, allow_methods=['*'], allow_headers=['*'])
@app.get('/')
def root(): return {'status':'ok'}

@app.get('/plants', response_model=list[schemas.PlantOut])
def list_plants(db: Session = Depends(get_db)):
    return crud.get_plants(db)

@app.post('/plants', response_model=schemas.PlantOut)
def add_plant(plant: schemas.PlantCreate, db: Session = Depends(get_db)):
    return crud.create_plant(db, plant)

@app.get('/metadata/tables/{plant_id}')
def metadata_tables(plant_id: int, db: Session = Depends(get_db)):
    p = crud.get_plant(db, plant_id)
    if not p: raise HTTPException(status_code=404)
    return ['CSDOCS','CSMETA_PROJECT','CSMETA_EQUIP']

@app.get('/metadata/columns/{plant_id}/{table_name}')
def metadata_columns(plant_id: int, table_name: str, db: Session = Depends(get_db)):
    p = crud.get_plant(db, plant_id)
    if not p: raise HTTPException(status_code=404)
    return [{'column_name':'RKEY','data_type':'VARCHAR2','nullable':'N'},{'column_name':'PROJ_ID','data_type':'VARCHAR2','nullable':'Y'},{'column_name':'DOCNAME','data_type':'VARCHAR2','nullable':'Y'},{'column_name':'DOCPATH','data_type':'VARCHAR2','nullable':'Y'}]

@app.post('/mappings')
def save_mapping(mapping: schemas.MappingIn, db: Session = Depends(get_db)):
    m = crud.save_mapping(db, mapping); return {'id': m.id}

@app.get('/mappings/{plant_id}')
def get_mappings(plant_id: int, db: Session = Depends(get_db)):
    rows = crud.get_mappings_for_plant(db, plant_id); return [{'id': r.id, 'metadata_table': r.metadata_table, 'field_mappings': r.field_mappings} for r in rows]

@app.post('/migrate/start')
def migrate_start(plant_id: int, mapping_id: int, limit: int | None = None, db: Session = Depends(get_db)):
    run = crud.create_migration_run(db, plant_id, mapping_id)
    task = migrate_plant_task.delay(plant_id, mapping_id, limit)
    crud.update_run_status(db, run.id, status='queued')
    return {'task_id': task.id, 'run_id': run.id}

@app.get('/migrate/runs')
def list_runs(db: Session = Depends(get_db)):
    rows = db.query(models.MigrationRun).order_by(models.MigrationRun.id.desc()).all()
    return [{'id': r.id, 'plant_id': r.plant_id, 'mapping_id': r.mapping_id, 'status': r.status, 'total': r.total, 'succeeded': r.succeeded, 'failed': r.failed} for r in rows]

@app.get('/migrate/runs/{run_id}/logs')
def run_logs(run_id: int, db: Session = Depends(get_db)):
    logs = crud.get_run_logs(db, run_id)
    return logs

@app.post('/migrate/runs/{run_id}/retry')
def retry_log(run_id: int, log_id: int, db: Session = Depends(get_db)):
    log = crud.get_log(db, log_id)
    if not log:
        raise HTTPException(status_code=404, detail='log not found')
    task = retry_log_task.delay(log_id)
    return {'task_id': task.id}

@app.get('/migrate/runs/{run_id}/export')
def export_run_csv(run_id: int, db: Session = Depends(get_db)):
    import csv, io
    logs = crud.get_run_logs(db, run_id)
    output = io.StringIO()
    writer = csv.writer(output)
    writer.writerow(['doc_dkey','doc_name','status','message','attempts','paperless_id','created_at'])
    for l in logs:
        writer.writerow([l.doc_dkey, l.doc_name, l.status, l.message, l.attempts, l.paperless_id, l.created_at])
    return Response(content=output.getvalue(), media_type='text/csv')
