from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from .database import Base
import datetime
class Plant(Base):
    __tablename__ = "plants"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(128), nullable=False)
    db_type = Column(String(32), default="oracle")
    host = Column(String(128), nullable=True)
    port = Column(String(16), nullable=True)
    service_name = Column(String(128), nullable=True)
    username = Column(String(128), nullable=True)
    password_enc = Column(Text, nullable=True)
    storage_path = Column(String(255), nullable=True)
    active = Column(Boolean, default=True)
    notes = Column(Text, nullable=True)
class Mapping(Base):
    __tablename__ = "mappings"
    id = Column(Integer, primary_key=True)
    plant_id = Column(Integer, nullable=False)
    metadata_table = Column(String(128), nullable=False)
    field_mappings = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
class MigrationRun(Base):
    __tablename__ = 'migration_runs'
    id = Column(Integer, primary_key=True)
    plant_id = Column(Integer, nullable=False)
    mapping_id = Column(Integer, nullable=False)
    status = Column(String(32), default='pending')
    started_at = Column(DateTime, nullable=True)
    finished_at = Column(DateTime, nullable=True)
    total = Column(Integer, default=0)
    succeeded = Column(Integer, default=0)
    failed = Column(Integer, default=0)
    logs = relationship('MigrationLog', back_populates='run')
class MigrationLog(Base):
    __tablename__ = 'migration_logs'
    id = Column(Integer, primary_key=True)
    run_id = Column(Integer, ForeignKey('migration_runs.id'))
    doc_dkey = Column(Integer, nullable=True)
    doc_name = Column(String(255), nullable=True)
    status = Column(String(32), nullable=False)
    message = Column(Text, nullable=True)
    paperless_id = Column(Integer, nullable=True)
    attempts = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    run = relationship('MigrationRun', back_populates='logs')
