from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
class PlantBase(BaseModel):
    name: str
    host: Optional[str] = None
    port: Optional[str] = None
    username: Optional[str] = None
class PlantCreate(PlantBase):
    password: Optional[str] = None
class PlantOut(PlantBase):
    id: int
    class Config:
        from_attributes = True
class MappingIn(BaseModel):
    plant_id: int
    metadata_table: str
    field_mappings: Dict[str, Any]
class MigrationRunOut(BaseModel):
    id: int
    plant_id: int
    mapping_id: int
    status: str
    total: int
    succeeded: int
    failed: int
    class Config:
        from_attributes = True
class MigrationLogOut(BaseModel):
    id: int
    run_id: int
    doc_name: Optional[str]
    status: str
    message: Optional[str]
    attempts: int
    created_at: datetime
    class Config:
        from_attributes = True
